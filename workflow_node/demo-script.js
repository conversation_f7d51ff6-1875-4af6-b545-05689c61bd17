// 演示脚本 - 用于测试多选下拉组件功能

// 等待页面加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，开始演示多选下拉组件功能');
    
    // 延迟执行演示，确保所有脚本都已加载
    setTimeout(function() {
        demonstrateFieldFilter();
    }, 1000);
});

function demonstrateFieldFilter() {
    console.log('开始演示字段过滤器功能');
    
    // 1. 选择查询数据节点
    console.log('1. 选择查询数据节点');
    selectNode('data-query');
    
    // 等待抽屉打开
    setTimeout(function() {
        // 2. 选择目标表单
        console.log('2. 选择目标表单');
        const targetFormSelect = document.getElementById('target-form');
        if (targetFormSelect) {
            targetFormSelect.value = 'user-form';
            // 触发change事件
            targetFormSelect.dispatchEvent(new Event('change'));
            
            // 等待字段过滤器更新
            setTimeout(function() {
                // 3. 测试字段过滤器
                console.log('3. 测试字段过滤器');
                testFieldFilterDropdown();
            }, 500);
        } else {
            console.error('未找到目标表单选择器');
        }
    }, 500);
}

function testFieldFilterDropdown() {
    const container = document.getElementById('field-filter-dropdown-container');
    if (!container) {
        console.error('未找到字段过滤器容器');
        return;
    }
    
    console.log('字段过滤器容器找到:', container);
    
    // 检查是否有多选下拉组件
    const dropdown = container.querySelector('.multi-select-dropdown');
    if (dropdown) {
        console.log('✅ 多选下拉组件已成功创建');
        
        // 测试点击展开
        const input = dropdown.querySelector('.multi-select-input');
        if (input) {
            console.log('测试点击展开下拉框');
            input.click();
            
            setTimeout(function() {
                const content = dropdown.querySelector('.multi-select-dropdown-content');
                if (content && content.classList.contains('show')) {
                    console.log('✅ 下拉框成功展开');
                    
                    // 测试选择选项
                    const options = content.querySelectorAll('.option-item');
                    console.log(`找到 ${options.length} 个选项`);
                    
                    if (options.length > 0) {
                        // 选择前几个选项
                        console.log('测试选择选项');
                        options[0].click();
                        if (options.length > 1) options[1].click();
                        if (options.length > 2) options[2].click();
                        
                        setTimeout(function() {
                            // 检查选中的标签
                            const tags = dropdown.querySelectorAll('.selected-tag');
                            console.log(`✅ 成功选择了 ${tags.length} 个选项`);
                            
                            // 测试搜索功能
                            const searchInput = content.querySelector('.search-input');
                            if (searchInput) {
                                console.log('测试搜索功能');
                                searchInput.value = '姓名';
                                searchInput.dispatchEvent(new Event('input'));
                                
                                setTimeout(function() {
                                    const visibleOptions = content.querySelectorAll('.option-item:not([style*="display: none"])');
                                    console.log(`搜索后显示 ${visibleOptions.length} 个选项`);
                                    
                                    // 清空搜索
                                    searchInput.value = '';
                                    searchInput.dispatchEvent(new Event('input'));
                                    
                                    // 测试全选和清空
                                    setTimeout(function() {
                                        testSelectAllAndClear(content);
                                    }, 300);
                                }, 300);
                            }
                        }, 300);
                    }
                } else {
                    console.error('❌ 下拉框未能展开');
                }
            }, 300);
        }
    } else {
        console.error('❌ 多选下拉组件未创建');
        
        // 检查是否还是旧的select元素
        const oldSelect = container.querySelector('select');
        if (oldSelect) {
            console.log('发现旧的select元素，可能组件未正确替换');
        }
    }
}

function testSelectAllAndClear(content) {
    console.log('测试全选和清空功能');
    
    const selectAllBtn = content.querySelector('[data-action="select-all"]');
    const clearAllBtn = content.querySelector('[data-action="clear-all"]');
    
    if (selectAllBtn) {
        console.log('测试全选');
        selectAllBtn.click();
        
        setTimeout(function() {
            const selectedOptions = content.querySelectorAll('.option-item.selected');
            console.log(`全选后有 ${selectedOptions.length} 个选项被选中`);
            
            if (clearAllBtn) {
                console.log('测试清空');
                clearAllBtn.click();
                
                setTimeout(function() {
                    const selectedOptionsAfterClear = content.querySelectorAll('.option-item.selected');
                    console.log(`清空后有 ${selectedOptionsAfterClear.length} 个选项被选中`);
                    
                    console.log('✅ 演示完成！');
                }, 300);
            }
        }, 300);
    }
}

// 添加到全局作用域以便在控制台中调用
window.demonstrateFieldFilter = demonstrateFieldFilter;
