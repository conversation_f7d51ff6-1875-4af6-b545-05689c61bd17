<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多选下拉组件测试</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .result-display {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>多选下拉组件测试页面</h1>
        
        <div class="test-section">
            <div class="test-title">基础多选测试</div>
            <div id="test1-container"></div>
            <div class="result-display" id="test1-result">选中值: []</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">字段过滤器测试（模拟实际使用场景）</div>
            <div id="test2-container"></div>
            <div class="result-display" id="test2-result">选中值: []</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">预设选中值测试</div>
            <div id="test3-container"></div>
            <div class="result-display" id="test3-result">选中值: []</div>
        </div>
    </div>

    <script src="js/multi-select-dropdown.js"></script>
    <script>
        // 测试数据
        const basicOptions = [
            { value: 'option1', text: '选项1' },
            { value: 'option2', text: '选项2' },
            { value: 'option3', text: '选项3' },
            { value: 'option4', text: '选项4' },
            { value: 'option5', text: '选项5' }
        ];

        const fieldOptions = [
            { value: 'name', text: '姓名 (name) - 文本' },
            { value: 'age', text: '年龄 (age) - 数字' },
            { value: 'email', text: '邮箱 (email) - 邮箱' },
            { value: 'phone', text: '电话 (phone) - 电话' },
            { value: 'address', text: '地址 (address) - 文本' },
            { value: 'birthday', text: '生日 (birthday) - 日期' },
            { value: 'gender', text: '性别 (gender) - 选择' },
            { value: 'skills', text: '技能 (skills) - 多选' },
            { value: 'experience.company', text: '工作经历 - 公司 (experience.company) - 文本' },
            { value: 'experience.position', text: '工作经历 - 职位 (experience.position) - 文本' },
            { value: 'experience.duration', text: '工作经历 - 时长 (experience.duration) - 数字' }
        ];

        // 测试1: 基础功能
        const test1 = new MultiSelectDropdown(
            document.getElementById('test1-container'),
            {
                options: basicOptions,
                placeholder: '请选择选项',
                onChange: (values) => {
                    document.getElementById('test1-result').textContent = `选中值: [${values.join(', ')}]`;
                }
            }
        );

        // 测试2: 字段过滤器场景
        const test2 = new MultiSelectDropdown(
            document.getElementById('test2-container'),
            {
                options: fieldOptions,
                placeholder: '选择要返回的字段（默认全部）',
                searchPlaceholder: '搜索字段...',
                onChange: (values) => {
                    document.getElementById('test2-result').textContent = `选中值: [${values.join(', ')}]`;
                }
            }
        );

        // 测试3: 预设选中值
        const test3 = new MultiSelectDropdown(
            document.getElementById('test3-container'),
            {
                options: fieldOptions,
                selectedValues: ['name', 'email', 'phone'],
                placeholder: '选择字段',
                onChange: (values) => {
                    document.getElementById('test3-result').textContent = `选中值: [${values.join(', ')}]`;
                }
            }
        );

        // 初始化结果显示
        document.getElementById('test2-result').textContent = `选中值: []`;
        document.getElementById('test3-result').textContent = `选中值: [${test3.getValue().join(', ')}]`;
    </script>
</body>
</html>
