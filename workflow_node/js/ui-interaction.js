// 表达式验证和提示功能

// 验证表达式输入
function validateExpressionInput(input) {
    const expression = input.value;
    const validator = input.closest('.expression-validator');
    const validationDiv = validator.querySelector('.expression-validation');
    
    if (!expression) {
        validationDiv.classList.remove('show');
        return;
    }
    
    // 显示验证结果
    validationDiv.classList.add('show');
    validationDiv.innerHTML = '';
    
    // 显示成功消息
    const successMsg = document.createElement('div');
    successMsg.className = 'validation-message success';
    successMsg.textContent = '表达式语法正确';
    validationDiv.appendChild(successMsg);
}


// 隐藏表达式验证
function hideExpressionValidation(input) {
    const validator = input.closest('.expression-validator');
    const validationDiv = validator.querySelector('.expression-validation');
    
    // 延迟隐藏，以便用户可以点击建议
    setTimeout(() => {
        if (!validationDiv.matches(':hover')) {
            validationDiv.classList.remove('show');
        }
    }, 200);
}


// 表达式自动完成功能
let currentAutocompleteIndex = -1;
let currentAutocompleteItems = [];

// 处理表达式输入
function handleExpressionInput(input) {
    // 隐藏自动完成
    hideAutocomplete(input);
}

// 处理键盘事件
function handleExpressionKeydown(event, input) {
    const validator = input.closest('.expression-validator');
    const autocompletePopup = validator.querySelector('.autocomplete-popup');
    
    if (!autocompletePopup || !autocompletePopup.classList.contains('show')) {
        return;
    }
    
    switch (event.key) {
        case 'ArrowDown':
            // 向下选择
            event.preventDefault();
            currentAutocompleteIndex = Math.min(currentAutocompleteIndex + 1, currentAutocompleteItems.length - 1);
            updateAutocompleteSelection();
            break;
            
        case 'ArrowUp':
            // 向上选择
            event.preventDefault();
            currentAutocompleteIndex = Math.max(currentAutocompleteIndex - 1, -1);
            updateAutocompleteSelection();
            break;
            
        case 'Enter':
            // 选择当前项
            event.preventDefault();
            if (currentAutocompleteIndex >= 0 && currentAutocompleteItems[currentAutocompleteIndex]) {
                selectAutocompleteItem(input, currentAutocompleteItems[currentAutocompleteIndex]);
            }
            break;
            
        case 'Escape':
            // 关闭自动完成
            event.preventDefault();
            hideAutocomplete(input);
            break;
            
        case 'Tab':
            // 选择当前项并继续编辑
            event.preventDefault();
            if (currentAutocompleteIndex >= 0 && currentAutocompleteItems[currentAutocompleteIndex]) {
                selectAutocompleteItem(input, currentAutocompleteItems[currentAutocompleteIndex]);
            }
            break;
    }
}

// 显示自动完成
function showAutocomplete(input, context) {
    // 不显示自动完成
    hideAutocomplete(input);
}

// 隐藏自动完成
function hideAutocomplete(input) {
    const validator = input.closest('.expression-validator');
    const autocompletePopup = validator.querySelector('.autocomplete-popup');
    
    if (autocompletePopup) {
        autocompletePopup.classList.remove('show');
    }
    currentAutocompleteIndex = -1;
    currentAutocompleteItems = [];
}

// 更新自动完成选择
function updateAutocompleteSelection() {
    const autocompleteItems = document.querySelectorAll('.autocomplete-item');
    autocompleteItems.forEach((item, index) => {
        item.classList.toggle('selected', index === currentAutocompleteIndex);
    });
}

// 选择自动完成项
function selectAutocompleteItem(input, suggestion) {
    // 获取光标位置
    const cursorPos = input.selectionStart;
    const text = input.value;
    const textBeforeCursor = text.substring(0, cursorPos);
    
    // 找到{{的位置
    const bracesMatch = textBeforeCursor.match(/\{\{[a-zA-Z_][a-zA-Z0-9_]*$/);
    const singleBraceMatch = textBeforeCursor.match(/\{$/);
    
    if (bracesMatch) {
        const bracesPos = cursorPos - bracesMatch[0].length;
        const newText = text.substring(0, bracesPos) + suggestion.text + text.substring(cursorPos);
        input.value = newText;
        
        // 设置光标位置
        const newCursorPos = bracesPos + suggestion.text.length;
        input.setSelectionRange(newCursorPos, newCursorPos);
    } else if (singleBraceMatch) {
        const bracePos = cursorPos - 1;
        const newText = text.substring(0, bracePos) + suggestion.text + text.substring(cursorPos);
        input.value = newText;
        
        // 设置光标位置
        const newCursorPos = bracePos + suggestion.text.length;
        input.setSelectionRange(newCursorPos, newCursorPos);
    }
    
    hideAutocomplete(input);
    validateExpressionInput(input);
}

// 事件处理函数

// 表单选择变化处理函数
function onTargetFormChange(operationType) {
    const targetForm = document.getElementById('target-form').value;
    const dynamicFieldsContainer = document.getElementById('dynamic-fields-container');
    
    if (targetForm && formFieldsData[targetForm]) {
        // 显示动态字段容器
        if (dynamicFieldsContainer) {
            dynamicFieldsContainer.style.display = 'block';
            // 生成字段列表
            generateDynamicFields(targetForm, operationType);
        }
    } else {
        // 隐藏动态字段容器
        if (dynamicFieldsContainer) {
            dynamicFieldsContainer.style.display = 'none';
        }
    }
    
    // 对于查询、删除和更新操作，更新筛选条件中的字段选项
    if ((operationType === 'query' || operationType === 'delete' || operationType === 'update') && targetForm) {
        updateFilterFieldOptions(targetForm);
        // 对于查询操作，更新字段过滤器选项
        if (operationType === 'query') {
            updateFieldFilterOptions(targetForm);
        }
        // 对于删除和更新操作，验证条件数量
        if (operationType === 'delete' || operationType === 'update') {
            validateFilterConditions();
        }
    }
    
    // 如果是更新操作，确保更新模式选择器正确显示
    if (operationType === 'update') {
        const updateModeSelector = document.querySelector('.update-mode-selector');
        if (updateModeSelector && targetForm) {
            updateModeSelector.style.display = 'block';
        } else if (updateModeSelector) {
            updateModeSelector.style.display = 'none';
        }
    }
}

// 更新筛选条件中的字段选项
function updateFilterFieldOptions(targetForm) {
    const fields = formFieldsData[targetForm] || [];
    const fieldOptions = generateFieldOptionsForFilter(fields);
    
    // 更新所有现有的字段选择器
    const fieldSelects = document.querySelectorAll('.field-select');
    fieldSelects.forEach(select => {
        const currentValue = select.value;
        select.innerHTML = `<option value="">选择字段</option>${fieldOptions}`;
        
        // 检查当前值是否仍然有效（包括子表单子字段）
        if (currentValue) {
            const isValidField = fields.some(field => {
                if (field.name === currentValue) return true;
                if (field.type === 'array' && field.itemSchema) {
                    return Object.keys(field.itemSchema).some(key => `${field.name}.${key}` === currentValue);
                }
                return false;
            });
            if (isValidField) {
                select.value = currentValue;
            }
        }
    });
}

// 生成筛选用的字段选项
function generateFieldOptionsForFilter(fields) {
    let options = '';
    fields.forEach(field => {
        const fieldTypeAbbr = getTypeAbbreviation(field.type);
        options += `<option value="${field.name}">${field.label} (${field.name}) - ${fieldTypeAbbr}</option>`;
        // 如果是子表单字段，添加其子字段
        if (field.type === 'array' && field.itemSchema) {
            Object.keys(field.itemSchema).forEach(key => {
                const subField = field.itemSchema[key];
                const subFieldTypeAbbr = getTypeAbbreviation(subField.type);
                options += `<option value="${field.name}.${key}">${field.label} - ${subField.label} (${field.name}.${key}) - ${subFieldTypeAbbr}</option>`;
            });
        }
    });
    return options;
}

// 生成动态字段
function generateDynamicFields(formKey, operationType) {
    const fields = formFieldsData[formKey];
    const fieldsList = document.getElementById('fields-list');
    
    // 获取当前更新模式
    const updateMode = document.querySelector('input[name="update-mode"]:checked')?.value || 'null';
    
    let html = '';
    // 添加输入参数标题行
    html += `
        <div class="param-row param-header-row">
            <div class="param-cell param-name-cell">字段名</div>
            <div class="param-cell param-type-cell">字段类型</div>
            <div class="param-cell param-value-cell">字段值</div>
        </div>
    `;
    
    fields.forEach(field => {
        const fieldId = `field_${field.name}`;
        // 根据模式决定是否显示必填标记
        const requiredMark = (updateMode === 'null' && field.required) ? '<span class="required-mark">*</span>' : '';
        const typeAbbr = getTypeAbbreviation(field.type);
        
        if (field.type === 'array' && field.itemSchema) {
            // 子表单类型字段特殊处理
            if (operationType === 'update') {
                // 更新数据节点中的子表单字段显示红色提示文字
                html += `
                    <div class="field-item-inline" data-field="${field.name}">
                        <span class="field-label">${field.label}${requiredMark}</span>
                        <span class="field-type field-type-${field.type}">${typeAbbr}</span>
                        <span class="array-field-warning" style="color: #e74c3c; font-size: 12px; font-style: italic;">更新数据不支持子表单字段更新</span>
                    </div>
                `;
            } else {
                // 其他操作类型使用正常的子表单字段处理
                html += generateArrayField(field, fieldId, updateMode);
            }
        } else {
            // 普通字段处理
            let placeholder = "表达式";
            // 如果是关联表单类型，生成包含示例 ID 的 placeholder
            if (field.type === 'related' && field.relatedFormId) {
                placeholder = `表达式, 目标表单实例 ID`;
            }
            html += `
                <div class="field-item-inline" data-field="${field.name}">
                    <span class="field-label">${field.label}${requiredMark}</span>
                    <span class="field-type field-type-${field.type}">${typeAbbr}</span>
                    <input type="text" id="${fieldId}" class="field-input-inline" placeholder="${placeholder}" title="${field.description || field.label}">
                </div>
            `;
        }
    });
    
    fieldsList.innerHTML = html;
}

// 生成子表单类型字段
function generateArrayField(field, fieldId, updateMode = 'null') {
    // 根据模式决定是否显示必填标记
    const requiredMark = (updateMode === 'null' && field.required) ? '<span class="required-mark">*</span>' : '';
    const typeAbbr = getTypeAbbreviation(field.type);
    const arrayId = `array_${field.name}`;
    
    return `
        <div class="array-field-container" data-field="${field.name}">
            <div class="array-field-header" onclick="toggleArrayField('${arrayId}')">
                <div class="array-field-title">
                    <span class="field-label">${field.label}${requiredMark}</span>
                    <span class="field-type field-type-${field.type}">${typeAbbr}</span>
                </div>
                <span class="array-toggle-icon">▶</span>
            </div>
            <div id="${arrayId}" class="array-field-content" style="display: none;">
                <div class="array-items" id="${arrayId}_items"></div>
                <button type="button" onclick="addArrayItem('${field.name}')" class="btn btn-outline-primary btn-sm">
                    + 添加记录
                </button>
            </div>
        </div>
    `;
}

// 切换子表单字段展开/收起
function toggleArrayField(arrayId) {
    const content = document.getElementById(arrayId);
    const icon = document.querySelector(`[onclick="toggleArrayField('${arrayId}')"] .array-toggle-icon`);
    
    if (content.style.display === 'none') {
        content.style.display = 'block';
        icon.textContent = '▼';
        
        // 如果是第一次展开，初始化子表单项
        const itemsContainer = document.getElementById(`${arrayId}_items`);
        if (itemsContainer.children.length === 0) {
            const fieldName = arrayId.replace('array_', '');
            initializeArrayItems(fieldName);
        }
    } else {
        content.style.display = 'none';
        icon.textContent = '▶';
    }
}

// 初始化子表单项
function initializeArrayItems(fieldName) {
    const field = findFieldByName(fieldName);
    if (!field || !field.itemSchema) return;
    
    const arrayId = `array_${fieldName}`;
    const itemsContainer = document.getElementById(`${arrayId}_items`);
    
    // 如果已有数据，可以在这里加载
    // 目前为空，用户需要点击"添加记录"按钮来添加
}

// 添加子表单项
function addArrayItem(fieldName) {
    const field = findFieldByName(fieldName);
    if (!field || !field.itemSchema) return;
    
    const arrayId = `array_${fieldName}`;
    const itemsContainer = document.getElementById(`${arrayId}_items`);
    const itemIndex = itemsContainer.children.length;
    const itemId = `${arrayId}_item_${itemIndex}`;
    
    const itemHtml = generateArrayItem(fieldName, field.itemSchema, itemId, itemIndex);
    itemsContainer.insertAdjacentHTML('beforeend', itemHtml);
}

// 生成子表单项
function generateArrayItem(fieldName, itemSchema, itemId, itemIndex) {
    // 获取当前更新模式
    const updateMode = document.querySelector('input[name="update-mode"]:checked')?.value || 'null';
    
    let html = `
        <div class="array-item" id="${itemId}">
            <div class="array-item-header">
                <div class="array-item-title">记录 ${itemIndex + 1}</div>
                <button type="button" onclick="removeArrayItem('${itemId}')" class="btn btn-outline-danger btn-sm">删除</button>
            </div>
            <div class="array-item-fields">
    `;
    
    // 生成子属性字段，使用与普通字段一致的内联样式
    Object.keys(itemSchema).forEach(key => {
        const subField = itemSchema[key];
        const subFieldId = `${itemId}_${key}`;
        // 根据模式决定是否显示必填标记
        const requiredMark = (updateMode === 'null' && subField.required) ? '<span class="required-mark">*</span>' : '';
        const typeAbbr = getTypeAbbreviation(subField.type);
        
        // 根据字段类型生成不同的输入控件，使用与普通字段一致的表达式输入方式
        let fieldInput = '';
        let placeholder = `表达式`;
        // 如果是关联表单类型，生成包含示例 ID 的 placeholder
        if (subField.type === 'related' && subField.relatedFormId) {
            placeholder = `表达式, 目标表单实例 ID`;
        }
        
        if (subField.type === 'textarea') {
            fieldInput = `
                <div class="expression-validator">
                    <textarea id="${subFieldId}" class="field-value-input expression-input" placeholder="${placeholder}" rows="2"
                        oninput="validateExpressionInput(this); handleExpressionInput(this)"
                        onblur="hideExpressionValidation(this)"
                        onkeydown="handleExpressionKeydown(event, this)"></textarea>
                    <div class="expression-validation" id="validation-${subFieldId}"></div>
                    <div class="autocomplete-popup" id="autocomplete-${subFieldId}"></div>
                </div>
            `;
        } else {
            fieldInput = `
                <div class="expression-validator">
                    <input type="text" id="${subFieldId}" class="field-value-input expression-input" placeholder="${placeholder}"
                        oninput="validateExpressionInput(this); handleExpressionInput(this)"
                        onblur="hideExpressionValidation(this)"
                        onkeydown="handleExpressionKeydown(event, this)">
                    <div class="expression-validation" id="validation-${subFieldId}"></div>
                    <div class="autocomplete-popup" id="autocomplete-${subFieldId}"></div>
                </div>
            `;
        }
        
        // 使用与普通字段一致的内联样式，一行一个字段
        html += `
            <div class="field-item-inline" data-field="${fieldName}_${key}">
                <span class="field-label">${subField.label}${requiredMark}</span>
                <span class="field-type field-type-${subField.type}">${typeAbbr}</span>
                ${fieldInput}
            </div>
        `;
    });
    
    html += `
            </div>
        </div>
    `;
    
    return html;
}

// 删除子表单项
function removeArrayItem(itemId) {
    const item = document.getElementById(itemId);
    if (item) {
        item.remove();
        
        // 重新编号剩余的项
        const arrayId = itemId.split('_item_')[0];
        const itemsContainer = document.getElementById(`${arrayId}_items`);
        const items = itemsContainer.querySelectorAll('.array-item');
        
        items.forEach((item, index) => {
            const titleElement = item.querySelector('.array-item-title');
            if (titleElement) {
                titleElement.textContent = `记录 ${index + 1}`;
            }
        });
    }
}

// 生成字段输入框
function generateFieldInput(field, fieldId) {
    const placeholder = `表达式`;
    
    if (field.type === 'textarea') {
        return `
            <div class="expression-validator">
                <textarea id="${fieldId}" class="field-value-input expression-input" placeholder="${placeholder}" rows="2"
                    oninput="validateExpressionInput(this); handleExpressionInput(this)"
                    onblur="hideExpressionValidation(this)"
                    onkeydown="handleExpressionKeydown(event, this)"></textarea>
                <div class="expression-validation" id="validation-${fieldId}"></div>
                <div class="autocomplete-popup" id="autocomplete-${fieldId}"></div>
            </div>
        `;
    } else {
        return `
            <div class="expression-validator">
                <input type="text" id="${fieldId}" class="field-value-input expression-input" placeholder="${placeholder}"
                        oninput="validateExpressionInput(this); handleExpressionInput(this)"
                    onblur="hideExpressionValidation(this)"
                    onkeydown="handleExpressionKeydown(event, this)">
                <div class="expression-validation" id="validation-${fieldId}"></div>
                <div class="autocomplete-popup" id="autocomplete-${fieldId}"></div>
            </div>
        `;
    }
}

// 获取字段占位符
function getFieldPlaceholder(field, valueType) {
    if (valueType === 'expression') {
        return `表达式`;
    } else {
        return field.placeholder || `请输入${field.label}`;
    }
}

// 字段值类型变化处理
// 字段值类型变化处理 - 已移除，统一使用表达式模式

// 根据名称查找字段定义
function findFieldByName(fieldName) {
    for (const formKey in formFieldsData) {
        const field = formFieldsData[formKey].find(f => f.name === fieldName);
        if (field) return field;
    }
    return null;
}

// 筛选条件相关功能
let filterConditionCounter = 0;

// 添加筛选条件
function addFilterCondition() {
    const container = document.getElementById('filter-rows-container');
    const conditionId = `condition_${++filterConditionCounter}`;
    
    const conditionDiv = document.createElement('div');
    conditionDiv.className = 'condition-row';
    conditionDiv.id = conditionId;
    
    // 如果不是第一个条件，添加连接符
    if (container.children.length > 0) {
        const connectorDiv = document.createElement('div');
        connectorDiv.className = 'condition-connector';
        connectorDiv.innerHTML = '且 (AND)';
        container.appendChild(connectorDiv);
    }
    
    conditionDiv.innerHTML = `
        <select class="field-select" onchange="onFilterFieldChange('${conditionId}')">
            <option value="">选择字段</option>
        </select>
        <select class="operator-select">
            <option value="eq">等于</option>
            <option value="ne">不等于</option>
            <option value="gt">大于</option>
            <option value="ge">大于等于</option>
            <option value="lt">小于</option>
            <option value="le">小于等于</option>
            <option value="contains">包含</option>
            <option value="startsWith">开头是</option>
            <option value="endsWith">结尾是</option>
        </select>
        <div class="value-input-container">
            <input type="text" class="value-input expression-input" placeholder="表达式，例如: {{form.name}} 或 'fixed value'">
        </div>
        <button type="button" onclick="removeFilterCondition('${conditionId}')" class="btn btn-small">-</button>
    `;
    
    document.getElementById('filter-rows-container').appendChild(conditionDiv);
    
    // 更新字段选项
    const targetForm = document.getElementById('target-form')?.value;
    if (targetForm) {
        updateFilterFieldOptions(targetForm);
    }
    
    // 验证条件数量
    validateFilterConditions();
}

// 移除筛选条件
function removeFilterCondition(conditionId) {
    const conditionElement = document.getElementById(conditionId);
    
    // 找到前面的连接符并删除
    let prevElement = conditionElement.previousElementSibling;
    if (prevElement && prevElement.classList.contains('condition-connector')) {
        prevElement.remove();
    } else {
        // 如果没有前面的连接符，删除后面的连接符
        let nextElement = conditionElement.nextElementSibling;
        if (nextElement && nextElement.classList.contains('condition-connector')) {
            nextElement.remove();
        }
    }
    
    conditionElement.remove();
    
    // 验证条件数量
    validateFilterConditions();
}

// 验证筛选条件数量
function validateFilterConditions() {
    const container = document.getElementById('filter-rows-container');
    const conditionRows = container.querySelectorAll('.condition-row');
    const conditionBuilder = document.getElementById('filter-conditions');
    
    // 检查是否需要验证（仅对更新和删除节点）
    if (!currentNode || (currentNode !== 'data-update' && currentNode !== 'data-delete')) {
        return;
    }
    
    // 移除现有的警告信息
    let existingWarning = conditionBuilder.querySelector('.condition-warning');
    if (existingWarning) {
        existingWarning.remove();
    }
    
    // 如果没有条件，显示警告
    if (conditionRows.length === 0) {
        const warningDiv = document.createElement('div');
        warningDiv.className = 'condition-warning';
        warningDiv.innerHTML = '<span class="warning-text">⚠️ 至少需要添加一个条件</span>';
        conditionBuilder.appendChild(warningDiv);
    }
}

// 筛选字段变化处理
function onFilterFieldChange(conditionId) {
    // 这里可以根据字段类型调整操作符选项
}

// 条件值类型变化处理 - 已移除，统一使用表达式模式

// 参数来源变化处理
function onParamSourceChange() {
    // 这里可以根据需要实现参数来源变化的逻辑
}

// 其他页面组件相关的事件处理函数
function onTargetFormSelectChange() {
    // 目标表单选择变化
    const formSelect = document.getElementById('target-form-select');
    const targetField = document.getElementById('target-field');
    
    if (!formSelect || !targetField) return;
    
    const selectedForm = formSelect.value;
    
    // 清空字段选择
    targetField.innerHTML = '<option value="">选择字段</option>';
    
    // 如果选择了表单，加载对应的字段
    if (selectedForm && formFieldsData[selectedForm]) {
        const fields = formFieldsData[selectedForm];
        fields.forEach(field => {
            // 添加主字段选项
            const option = document.createElement('option');
            option.value = field.name;
            option.textContent = `${field.label} (${field.name}) - ${getTypeAbbreviation(field.type)}`;
            targetField.appendChild(option);
            
            // 不再添加子表单的子属性选项
        });
    }
    
    // 重置显示状态
    const expressionConfig = document.getElementById('expression-config');
    const subformConfig = document.getElementById('subform-config');
    if (expressionConfig) expressionConfig.style.display = 'block';
    if (subformConfig) subformConfig.style.display = 'none';
}

function onTargetFieldChange() {
    // 目标字段变化
    const targetField = document.getElementById('target-field');
    const expressionConfig = document.getElementById('expression-config');
    const subformConfig = document.getElementById('subform-config');
    
    if (!targetField || !expressionConfig || !subformConfig) return;
    
    const selectedValue = targetField.value;
    
    if (selectedValue) {
        // 解析字段名称，可能是子表单字段（如 work_history 或 work_history.company）
        const [fieldName, subFieldName] = selectedValue.split('.');
        const field = findFieldByName(fieldName);
        
        if (field && field.type === 'array' && field.itemSchema && !subFieldName) {
            // 如果是子表单字段本身（不是子字段），显示子表单编辑界面
            expressionConfig.style.display = 'none';
            subformConfig.style.display = 'block';
            initializeSubformEditor(fieldName, field);
        } else {
            // 普通字段或子表单的子字段，显示表达式输入
            expressionConfig.style.display = 'block';
            subformConfig.style.display = 'none';
        }
    } else {
        // 没有选择字段，显示表达式输入
        expressionConfig.style.display = 'block';
        subformConfig.style.display = 'none';
    }
}

// 值类型变化处理 - 已移除，统一使用表达式模式

function loadPropertyPageComponentFields() {
    // 属性配置加载个人简历表单字段
    const targetComponent = document.getElementById('target-component-prop');
    
    if (!targetComponent) return;
    
    // 清空页面组件选择
    targetComponent.innerHTML = '<option value="">选择页面组件字段</option>';
    
    // 加载个人简历表单的字段
    if (formFieldsData['resume-form']) {
        const fields = formFieldsData['resume-form'];
        fields.forEach(field => {
            // 添加主字段选项
            const option = document.createElement('option');
            option.value = field.name;
            option.textContent = `${field.label} (${field.name}) - ${getTypeAbbreviation(field.type)}`;
            targetComponent.appendChild(option);
            
            // 如果是子表单字段，添加其子字段
            if (field.type === 'array' && field.itemSchema) {
                Object.keys(field.itemSchema).forEach(key => {
                    const subField = field.itemSchema[key];
                    const subOption = document.createElement('option');
                    subOption.value = `${field.name}.${key}`;
                    subOption.textContent = `${field.label} - ${subField.label} (${field.name}.${key}) - ${getTypeAbbreviation(subField.type)}`;
                    targetComponent.appendChild(subOption);
                });
            }
        });
    }
}

function loadVisibilityPageComponentFields() {
    // 可见性配置加载个人简历表单字段
    const targetComponent = document.getElementById('target-component-visibility');
    
    if (!targetComponent) return;
    
    // 清空页面组件选择
    targetComponent.innerHTML = '<option value="">选择页面组件字段</option>';
    
    // 加载个人简历表单的字段
    if (formFieldsData['resume-form']) {
        const fields = formFieldsData['resume-form'];
        fields.forEach(field => {
            // 添加主字段选项
            const option = document.createElement('option');
            option.value = field.name;
            option.textContent = `${field.label} (${field.name}) - ${getTypeAbbreviation(field.type)}`;
            targetComponent.appendChild(option);
            
            // 如果是子表单字段，添加其子字段
            if (field.type === 'array' && field.itemSchema) {
                Object.keys(field.itemSchema).forEach(key => {
                    const subField = field.itemSchema[key];
                    const subOption = document.createElement('option');
                    subOption.value = `${field.name}.${key}`;
                    subOption.textContent = `${field.label} - ${subField.label} (${field.name}.${key}) - ${getTypeAbbreviation(subField.type)}`;
                    targetComponent.appendChild(subOption);
                });
            }
        });
    }
}

// 向后兼容的函数别名
function onTargetFormPropertyChange() {
    loadPropertyPageComponentFields();
}

function onTargetFormVisibilityChange() {
    loadVisibilityPageComponentFields();
}

// 验证变量名称
function validateVariableName(input) {
    const value = input.value.trim();
    const regex = /^[a-zA-Z_][a-zA-Z0-9_]*$/;
    
    if (!value) {
        input.classList.remove('error', 'success');
        return false;
    }
    
    if (regex.test(value)) {
        input.classList.remove('error');
        input.classList.add('success');
        return true;
    } else {
        input.classList.remove('success');
        input.classList.add('error');
        return false;
    }
}

// 获取输出参数的变量名称
function getOutputVariableNames() {
    const variableInputs = document.querySelectorAll('.variable-input');
    const variableNames = {};
    
    variableInputs.forEach(input => {
        const originalName = input.getAttribute('data-original-name');
        const variableName = input.value.trim();
        
        if (variableName) {
            variableNames[originalName] = `result.${variableName}`;
        }
    });
    
    return variableNames;
}

// 获取完整的输出参数配置
function getOutputParamsConfig() {
    const variableNames = getOutputVariableNames();
    const config = {};
    
    Object.keys(variableNames).forEach(originalName => {
        config[originalName] = variableNames[originalName];
    });
    
    return config;
}

// 更新模式切换事件处理函数
function onUpdateModeChange(mode) {
    // 获取当前选中的表单
    const targetForm = document.getElementById('target-form')?.value;
    
    if (targetForm) {
        // 重新生成动态字段以更新必填标记显示
        generateDynamicFields(targetForm, 'update');
    }
}

// 字段过滤器相关函数
let fieldFilterDropdown = null;

// 更新字段过滤器选项
function updateFieldFilterOptions(targetForm) {
    const container = document.getElementById('field-filter-dropdown-container');
    if (!container) return;

    const fields = formFieldsData[targetForm] || [];
    const options = generateFieldOptionsForDropdown(fields);

    // 保存当前选中的值
    const selectedValues = fieldFilterDropdown ? fieldFilterDropdown.getValue() : [];

    // 销毁旧的下拉组件
    if (fieldFilterDropdown) {
        fieldFilterDropdown.destroy();
    }

    // 创建新的下拉组件
    fieldFilterDropdown = new MultiSelectDropdown(container, {
        options: options,
        selectedValues: selectedValues,
        placeholder: '选择要返回的字段（默认全部）',
        searchPlaceholder: '搜索字段...',
        onChange: (values) => {
            updateSelectedFieldsList();
            // 触发change事件以保持兼容性
            onFieldFilterChange();
        }
    });

    // 更新已选择字段列表
    updateSelectedFieldsList();
}

// 生成下拉组件用的字段选项
function generateFieldOptionsForDropdown(fields) {
    const options = [];
    fields.forEach(field => {
        const fieldTypeAbbr = getTypeAbbreviation(field.type);
        options.push({
            value: field.name,
            text: `${field.label} (${field.name}) - ${fieldTypeAbbr}`
        });

        // 如果是子表单字段，添加其子字段
        if (field.type === 'array' && field.itemSchema) {
            Object.keys(field.itemSchema).forEach(key => {
                const subField = field.itemSchema[key];
                const subFieldTypeAbbr = getTypeAbbreviation(subField.type);
                options.push({
                    value: `${field.name}.${key}`,
                    text: `${field.label} - ${subField.label} (${field.name}.${key}) - ${subFieldTypeAbbr}`
                });
            });
        }
    });

    return options;
}

// 字段过滤器选择变化处理
function onFieldFilterChange() {
    updateSelectedFieldsList();
}

// 更新已选择字段列表
function updateSelectedFieldsList() {
    const selectedList = document.getElementById('field-filter-selected-list');
    if (!selectedList) return;

    const selectedValues = fieldFilterDropdown ? fieldFilterDropdown.getValue() : [];

    if (selectedValues.length === 0) {
        selectedList.innerHTML = '<div class="field-filter-empty">未选择任何字段（将返回全部字段）</div>';
    } else {
        const fieldNames = selectedValues.map(value => {
            // 从下拉组件的选项中找到对应的文本
            const option = fieldFilterDropdown.options.find(opt => opt.value === value);
            if (option) {
                // 提取字段名称部分（去除类型等信息）
                const text = option.text;
                const match = text.match(/^(.+?)\s*\(/);
                return match ? match[1] : text;
            }
            return value;
        });
        selectedList.innerHTML = fieldNames.map(name => `<span class="field-filter-tag">${name}</span>`).join('');
    }
}

// 全选字段（兼容性函数）
function selectAllFields() {
    if (fieldFilterDropdown) {
        fieldFilterDropdown.selectAll();
    }
}

// 清空选择（兼容性函数）
function clearAllFields() {
    if (fieldFilterDropdown) {
        fieldFilterDropdown.clearAll();
    }
}

// 重置字段过滤器（兼容性函数）
function resetFieldFilter() {
    if (fieldFilterDropdown) {
        fieldFilterDropdown.clearAll();
    }
}

// 获取字段过滤器配置
function getFieldFilterConfig() {
    if (!fieldFilterDropdown) return null;

    const selectedValues = fieldFilterDropdown.getValue();

    if (selectedValues.length === 0) {
        return null; // 返回 null 表示选择全部字段
    }

    return selectedValues;
}

// 初始化子表单编辑器
function initializeSubformEditor(fieldName, field) {
    const container = document.getElementById('subform-container');
    if (!container || !field || !field.itemSchema) return;
    
    // 生成子表单编辑器HTML
    const subformId = `subform_${fieldName}`;
    let html = `
        <div class="subform-editor" data-field="${fieldName}">
            <div class="subform-items" id="${subformId}_items"></div>
            <button type="button" onclick="addSubformItem('${fieldName}')" class="btn btn-outline-primary btn-sm">
                + 添加记录
            </button>
        </div>
    `;
    
    container.innerHTML = html;
    
    // 初始化子表单项，加载现有数据或创建3条空记录
    initializeSubformItems(fieldName);
}

// 初始化子表单项
function initializeSubformItems(fieldName) {
    const field = findFieldByName(fieldName);
    if (!field || !field.itemSchema) return;
    
    const subformId = `subform_${fieldName}`;
    const itemsContainer = document.getElementById(`${subformId}_items`);
    if (!itemsContainer) return;
    
    // 尝试从表单数据中加载现有记录
    const existingData = loadSubformData(fieldName);
    
    if (existingData && existingData.length > 0) {
        // 如果有现有数据，加载这些记录
        existingData.forEach((itemData, index) => {
            addSubformItem(fieldName, itemData, index);
        });
    }
    // 不再默认创建3条空记录，保持为空，用户需要手动添加
}

// 加载子表单数据
function loadSubformData(fieldName) {
    // 这里可以从表单数据中加载现有记录
    // 目前返回null，表示没有现有数据，会创建3条空记录
    // 在实际应用中，这里可以从全局状态或API加载数据
    return null;
}

// 添加子表单项
function addSubformItem(fieldName, itemData = null, itemIndex = null) {
    const field = findFieldByName(fieldName);
    if (!field || !field.itemSchema) return;
    
    const subformId = `subform_${fieldName}`;
    const itemsContainer = document.getElementById(`${subformId}_items`);
    if (!itemsContainer) return;
    
    const index = itemIndex !== null ? itemIndex : itemsContainer.children.length;
    const itemId = `${subformId}_item_${index}`;
    
    const itemHtml = generateSubformItem(fieldName, field.itemSchema, itemId, index, itemData);
    itemsContainer.insertAdjacentHTML('beforeend', itemHtml);
}

// 生成子表单项
function generateSubformItem(fieldName, itemSchema, itemId, itemIndex, itemData = null) {
    let html = `
        <div class="subform-item" id="${itemId}">
            <div class="subform-item-header">
                <div class="subform-item-title">记录 ${itemIndex + 1}</div>
                <button type="button" onclick="removeSubformItem('${itemId}')" class="btn btn-outline-danger btn-sm">删除</button>
            </div>
            <div class="subform-item-fields">
    `;
    
    // 生成子属性字段，使用与普通字段一致的内联样式
    Object.keys(itemSchema).forEach(key => {
        const subField = itemSchema[key];
        const subFieldId = `${itemId}_${key}`;
        const requiredMark = subField.required ? '<span class="required-mark">*</span>' : '';
        const typeAbbr = getTypeAbbreviation(subField.type);
        
        // 根据字段类型生成不同的输入控件，使用与普通字段一致的表达式输入方式
        let fieldInput = '';
        let placeholder = `表达式`;
        // 如果是关联表单类型，生成包含示例 ID 的 placeholder
        if (subField.type === 'related' && subField.relatedFormId) {
            placeholder = `表达式, 目标表单实例 ID`;
        }
        const value = itemData && itemData[key] ? itemData[key] : '';
        
        if (subField.type === 'textarea') {
            fieldInput = `
                <div class="expression-validator">
                    <textarea id="${subFieldId}" class="field-value-input expression-input" placeholder="${placeholder}" rows="2"
                        oninput="validateExpressionInput(this); handleExpressionInput(this)"
                        onblur="hideExpressionValidation(this)"
                        onkeydown="handleExpressionKeydown(event, this)">${value}</textarea>
                    <div class="expression-validation" id="validation-${subFieldId}"></div>
                    <div class="autocomplete-popup" id="autocomplete-${subFieldId}"></div>
                </div>
            `;
        } else {
            fieldInput = `
                <div class="expression-validator">
                    <input type="text" id="${subFieldId}" class="field-value-input expression-input" placeholder="${placeholder}" value="${value}"
                        oninput="validateExpressionInput(this); handleExpressionInput(this)"
                        onblur="hideExpressionValidation(this)"
                        onkeydown="handleExpressionKeydown(event, this)">
                    <div class="expression-validation" id="validation-${subFieldId}"></div>
                    <div class="autocomplete-popup" id="autocomplete-${subFieldId}"></div>
                </div>
            `;
        }
        
        // 使用与普通字段一致的内联样式，一行一个字段
        html += `
            <div class="field-item-inline" data-field="${fieldName}_${key}">
                <span class="field-label">${subField.label}${requiredMark}</span>
                <span class="field-type field-type-${subField.type}">${typeAbbr}</span>
                ${fieldInput}
            </div>
        `;
    });
    
    html += `
            </div>
        </div>
    `;
    
    return html;
}

// 删除子表单项
function removeSubformItem(itemId) {
    const item = document.getElementById(itemId);
    if (item) {
        item.remove();
        
        // 重新编号剩余的项
        const subformId = itemId.split('_item_')[0];
        const itemsContainer = document.getElementById(`${subformId}_items`);
        const items = itemsContainer.querySelectorAll('.subform-item');
        
        items.forEach((item, index) => {
            const titleElement = item.querySelector('.subform-item-title');
            if (titleElement) {
                titleElement.textContent = `记录 ${index + 1}`;
            }
        });
    }
}

// 子表单模式切换处理函数
function onSubformModeChange(mode) {
    // 获取当前子表单字段名称
    const subformConfig = document.getElementById('subform-config');
    if (!subformConfig || subformConfig.style.display === 'none') return;
    
    const subformContainer = document.getElementById('subform-container');
    if (!subformContainer) return;
    
    const subformEditor = subformContainer.querySelector('.subform-editor');
    if (!subformEditor) return;
    
    const fieldName = subformEditor.getAttribute('data-field');
    if (!fieldName) return;
    
    // 清空已添加的记录
    const subformId = `subform_${fieldName}`;
    const itemsContainer = document.getElementById(`${subformId}_items`);
    if (itemsContainer) {
        itemsContainer.innerHTML = '';
    }
}