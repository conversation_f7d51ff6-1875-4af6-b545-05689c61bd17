/**
 * 多选下拉组件
 * 提供现代化的多选下拉功能，支持搜索、标签显示等
 */
class MultiSelectDropdown {
    constructor(container, options = {}) {
        this.container = container;
        this.options = options.options || [];
        this.selectedValues = new Set(options.selectedValues || []);
        this.placeholder = options.placeholder || '请选择字段';
        this.searchPlaceholder = options.searchPlaceholder || '搜索字段...';
        this.onChange = options.onChange || (() => {});
        
        this.isOpen = false;
        this.filteredOptions = [...this.options];
        
        this.init();
    }
    
    init() {
        this.render();
        this.bindEvents();
    }
    
    render() {
        this.container.innerHTML = `
            <div class="multi-select-dropdown">
                <div class="multi-select-input" data-action="toggle">
                    <div class="selected-tags">
                        ${this.renderSelectedTags()}
                        ${this.selectedValues.size === 0 ? `<span class="multi-select-placeholder">${this.placeholder}</span>` : ''}
                    </div>
                    <div class="dropdown-arrow">▼</div>
                </div>
                <div class="multi-select-dropdown-content">
                    <div class="search-container">
                        <input type="text" class="search-input" placeholder="${this.searchPlaceholder}" data-action="search">
                    </div>
                    <div class="options-container">
                        ${this.renderOptions()}
                    </div>
                    <div class="dropdown-actions">
                        <button type="button" class="dropdown-action-btn" data-action="select-all">全选</button>
                        <button type="button" class="dropdown-action-btn" data-action="clear-all">清空</button>
                    </div>
                </div>
            </div>
        `;
    }
    
    renderSelectedTags() {
        return Array.from(this.selectedValues).map(value => {
            const option = this.options.find(opt => opt.value === value);
            if (!option) return '';
            
            return `
                <span class="selected-tag">
                    <span class="selected-tag-text" title="${option.text}">${option.text}</span>
                    <button type="button" class="selected-tag-remove" data-action="remove-tag" data-value="${value}">×</button>
                </span>
            `;
        }).join('');
    }
    
    renderOptions() {
        if (this.filteredOptions.length === 0) {
            return '<div class="no-options">没有找到匹配的选项</div>';
        }
        
        return this.filteredOptions.map(option => `
            <div class="option-item ${this.selectedValues.has(option.value) ? 'selected' : ''}" 
                 data-action="toggle-option" data-value="${option.value}">
                <div class="option-checkbox">
                    ${this.selectedValues.has(option.value) ? '✓' : ''}
                </div>
                <div class="option-text" title="${option.text}">${option.text}</div>
            </div>
        `).join('');
    }
    
    bindEvents() {
        // 事件委托
        this.container.addEventListener('click', this.handleClick.bind(this));
        this.container.addEventListener('input', this.handleInput.bind(this));
        
        // 点击外部关闭下拉框
        document.addEventListener('click', this.handleOutsideClick.bind(this));
        
        // 键盘事件
        this.container.addEventListener('keydown', this.handleKeydown.bind(this));
    }
    
    handleClick(event) {
        const action = event.target.dataset.action;
        
        switch (action) {
            case 'toggle':
                this.toggleDropdown();
                break;
            case 'toggle-option':
                this.toggleOption(event.target.closest('.option-item').dataset.value);
                break;
            case 'remove-tag':
                this.removeTag(event.target.dataset.value);
                event.stopPropagation();
                break;
            case 'select-all':
                this.selectAll();
                break;
            case 'clear-all':
                this.clearAll();
                break;
        }
    }
    
    handleInput(event) {
        if (event.target.dataset.action === 'search') {
            this.filterOptions(event.target.value);
        }
    }
    
    handleOutsideClick(event) {
        if (!this.container.contains(event.target)) {
            this.closeDropdown();
        }
    }
    
    handleKeydown(event) {
        if (event.key === 'Escape') {
            this.closeDropdown();
        }
    }
    
    toggleDropdown() {
        if (this.isOpen) {
            this.closeDropdown();
        } else {
            this.openDropdown();
        }
    }
    
    openDropdown() {
        this.isOpen = true;
        const input = this.container.querySelector('.multi-select-input');
        const content = this.container.querySelector('.multi-select-dropdown-content');
        
        input.classList.add('active');
        content.classList.add('show');
        
        // 聚焦搜索框
        const searchInput = this.container.querySelector('.search-input');
        setTimeout(() => searchInput.focus(), 100);
    }
    
    closeDropdown() {
        this.isOpen = false;
        const input = this.container.querySelector('.multi-select-input');
        const content = this.container.querySelector('.multi-select-dropdown-content');
        
        input.classList.remove('active');
        content.classList.remove('show');
        
        // 清空搜索
        const searchInput = this.container.querySelector('.search-input');
        searchInput.value = '';
        this.filteredOptions = [...this.options];
        this.updateOptionsDisplay();
    }
    
    toggleOption(value) {
        if (this.selectedValues.has(value)) {
            this.selectedValues.delete(value);
        } else {
            this.selectedValues.add(value);
        }
        
        this.updateDisplay();
        this.onChange(Array.from(this.selectedValues));
    }
    
    removeTag(value) {
        this.selectedValues.delete(value);
        this.updateDisplay();
        this.onChange(Array.from(this.selectedValues));
    }
    
    selectAll() {
        this.filteredOptions.forEach(option => {
            this.selectedValues.add(option.value);
        });
        
        this.updateDisplay();
        this.onChange(Array.from(this.selectedValues));
    }
    
    clearAll() {
        this.selectedValues.clear();
        this.updateDisplay();
        this.onChange(Array.from(this.selectedValues));
    }
    
    filterOptions(searchTerm) {
        const term = searchTerm.toLowerCase();
        this.filteredOptions = this.options.filter(option => 
            option.text.toLowerCase().includes(term) || 
            option.value.toLowerCase().includes(term)
        );
        
        this.updateOptionsDisplay();
    }
    
    updateDisplay() {
        const tagsContainer = this.container.querySelector('.selected-tags');
        tagsContainer.innerHTML = this.renderSelectedTags() + 
            (this.selectedValues.size === 0 ? `<span class="multi-select-placeholder">${this.placeholder}</span>` : '');
        
        this.updateOptionsDisplay();
    }
    
    updateOptionsDisplay() {
        const optionsContainer = this.container.querySelector('.options-container');
        optionsContainer.innerHTML = this.renderOptions();
    }
    
    // 公共API方法
    setOptions(options) {
        this.options = options;
        this.filteredOptions = [...options];
        this.updateDisplay();
    }
    
    getValue() {
        return Array.from(this.selectedValues);
    }
    
    setValue(values) {
        this.selectedValues = new Set(values || []);
        this.updateDisplay();
    }
    
    destroy() {
        document.removeEventListener('click', this.handleOutsideClick.bind(this));
        this.container.innerHTML = '';
    }
}

// 导出到全局作用域
window.MultiSelectDropdown = MultiSelectDropdown;
